# UserEngine Integration Guide - Mir200 Phase 1

## Overview

This guide provides detailed instructions for integrating the UserEngine into the Mir200 server system. The UserEngine is the core component responsible for managing all player-related operations, monster AI, NPC processing, and game world coordination.

## Integration Steps

### 1. Basic Integration

#### Include Headers
```cpp
#include "Engine/UserEngine.h"
#include "Common/M2Share.h"
```

#### Create UserEngine Instance
```cpp
// In M2Server.h
class M2Server {
private:
    std::unique_ptr<UserEngine> m_user_engine;
    
public:
    UserEngine* GetUserEngine() { return m_user_engine.get(); }
};

// In M2Server.cpp constructor
M2Server::M2Server() {
    m_user_engine = std::make_unique<UserEngine>();
}
```

#### Initialize UserEngine
```cpp
bool M2Server::Initialize() {
    // Initialize UserEngine
    if (!m_user_engine->Initialize()) {
        g_functions::MainOutMessage("Failed to initialize UserEngine");
        return false;
    }
    
    // Start UserEngine
    if (!m_user_engine->Start()) {
        g_functions::MainOutMessage("Failed to start UserEngine");
        return false;
    }
    
    return true;
}
```

### 2. Main Loop Integration

#### Process UserEngine in Main Loop
```cpp
void M2Server::ProcessData() {
    // Process UserEngine
    if (m_user_engine && m_user_engine->IsRunning()) {
        m_user_engine->ProcessData();
    }
    
    // Perform maintenance periodically
    static DWORD last_maintenance = 0;
    DWORD current_tick = GetTickCount();
    if ((current_tick - last_maintenance) > 300000) {  // 5 minutes
        last_maintenance = current_tick;
        m_user_engine->PerformMaintenance();
    }
}
```

### 3. Player Management Integration

#### Handle Player Login
```cpp
void M2Server::HandlePlayerLogin(const std::string& account, const std::string& char_name, 
                                int socket, int gate_idx, DWORD session_id) {
    // Create user open info
    auto user_info = std::make_shared<UserOpenInfo>();
    user_info->account = account;
    user_info->char_name = char_name;
    user_info->load_user.socket = socket;
    user_info->load_user.gate_idx = gate_idx;
    user_info->load_user.session_id = session_id;
    
    // Load player data from database
    LoadPlayerData(user_info->hum_data, char_name);
    
    // Add to UserEngine
    m_user_engine->AddUserOpenInfo(user_info);
}
```

#### Handle Player Logout
```cpp
void M2Server::HandlePlayerLogout(const std::string& char_name) {
    auto player = m_user_engine->GetPlayObject(char_name);
    if (player) {
        player->MakeGhost();
        g_functions::MainOutMessage("Player logged out: " + char_name);
    }
}
```

### 4. Message Processing Integration

#### Process Player Messages
```cpp
void M2Server::ProcessPlayerMessage(const std::string& char_name, DefaultMessage* msg, char* buff) {
    auto player = m_user_engine->GetPlayObject(char_name);
    if (player) {
        m_user_engine->ProcessUserMessage(player, msg, buff);
    }
}
```

### 5. Statistics and Monitoring Integration

#### Get Server Statistics
```cpp
void M2Server::ShowServerStatus() {
    ServerStatistics stats;
    m_user_engine->GetServerStatistics(stats);
    
    g_functions::MainOutMessage("=== Server Statistics ===");
    g_functions::MainOutMessage("Online Players: " + std::to_string(stats.online_players));
    g_functions::MainOutMessage("Total Players: " + std::to_string(stats.total_players));
    g_functions::MainOutMessage("Loading Players: " + std::to_string(stats.loading_players));
    g_functions::MainOutMessage("Total Monsters: " + std::to_string(stats.total_monsters));
    g_functions::MainOutMessage("Total Merchants: " + std::to_string(stats.total_merchants));
    g_functions::MainOutMessage("Total NPCs: " + std::to_string(stats.total_npcs));
    g_functions::MainOutMessage("Memory Usage: " + std::to_string(stats.memory_usage_mb) + " MB");
    g_functions::MainOutMessage("========================");
}
```

### 6. Shutdown Integration

#### Graceful Shutdown
```cpp
void M2Server::Shutdown() {
    if (m_user_engine) {
        g_functions::MainOutMessage("Shutting down UserEngine...");
        
        // Save all user data
        m_user_engine->SaveAllUsers();
        
        // Stop UserEngine
        m_user_engine->Stop();
        
        // Finalize UserEngine
        m_user_engine->Finalize();
        
        g_functions::MainOutMessage("UserEngine shutdown completed");
    }
}
```

### 7. Error Handling Integration

#### Handle Server State Changes
```cpp
void M2Server::OnServerStateChanged(ServerState new_state) {
    if (m_user_engine) {
        m_user_engine->OnServerStateChanged(new_state);
    }
    
    switch (new_state) {
        case ServerState::STOPPING:
            // Prepare for shutdown
            m_user_engine->SaveAllUsers();
            break;
            
        case ServerState::SERVER_ERROR:
            // Emergency stop
            m_user_engine->EmergencyStop();
            break;
            
        default:
            break;
    }
}
```

## Configuration Integration

### 1. Load Configuration Data

#### Load Items and Magic
```cpp
bool M2Server::LoadGameData() {
    // Load standard items
    if (!LoadStandardItems()) {
        return false;
    }
    
    // Load magic list
    if (!LoadMagicList()) {
        return false;
    }
    
    // Load monster information
    if (!LoadMonsterInfo()) {
        return false;
    }
    
    // Switch magic list in UserEngine
    m_user_engine->SwitchMagicList();
    
    return true;
}
```

### 2. Map Integration

#### Initialize Maps
```cpp
bool M2Server::InitializeMaps() {
    // Load map files
    if (!g_MapManager.LoadMaps()) {
        return false;
    }
    
    // Initialize merchants and NPCs
    m_user_engine->MerchantInitialize();
    m_user_engine->NPCInitialize();
    
    return true;
}
```

## Performance Optimization

### 1. Threading Considerations

The UserEngine is designed to be thread-safe for basic operations:

```cpp
// Safe to call from multiple threads
int online_count = m_user_engine->GetOnlinePlayObjectCount();
auto player = m_user_engine->GetPlayObject(char_name);

// Use locks for complex operations
{
    std::lock_guard<std::mutex> lock(user_engine_mutex);
    m_user_engine->AddUserOpenInfo(user_info);
}
```

### 2. Memory Management

```cpp
// Regular cleanup
void M2Server::PerformRegularMaintenance() {
    // Clean up expired sessions
    m_user_engine->CleanupExpiredSessions();
    
    // Compact data structures
    m_user_engine->CompactDataStructures();
    
    // Update statistics
    m_user_engine->UpdateStatistics();
}
```

### 3. Performance Monitoring

```cpp
void M2Server::MonitorPerformance() {
    ServerStatistics stats;
    m_user_engine->GetServerStatistics(stats);
    
    // Check for performance issues
    if (stats.memory_usage_mb > 1000) {  // 1GB limit
        g_functions::MainOutMessage("Warning: High memory usage detected");
        m_user_engine->PerformMaintenance();
    }
    
    if (stats.online_players > 5000) {  // Player limit
        g_functions::MainOutMessage("Warning: High player count detected");
    }
}
```

## Troubleshooting

### Common Issues

1. **UserEngine fails to initialize**
   - Check if all required data files are loaded
   - Verify map files are accessible
   - Ensure proper permissions

2. **High memory usage**
   - Call `PerformMaintenance()` more frequently
   - Check for memory leaks in custom code
   - Monitor `ServerStatistics.memory_usage_mb`

3. **Poor performance**
   - Reduce processing time limits if needed
   - Optimize custom message handlers
   - Monitor processing time statistics

### Debug Information

```cpp
void M2Server::ShowDebugInfo() {
    ServerStatistics stats;
    m_user_engine->GetServerStatistics(stats);
    
    g_functions::MainOutMessage("Debug Info:");
    g_functions::MainOutMessage("- Human process time: " + std::to_string(stats.human_process_time));
    g_functions::MainOutMessage("- Merchant process time: " + std::to_string(stats.merchant_process_time_min) + 
                               "-" + std::to_string(stats.merchant_process_time_max) + "ms");
    g_functions::MainOutMessage("- NPC process time: " + std::to_string(stats.npc_process_time_min) + 
                               "-" + std::to_string(stats.npc_process_time_max) + "ms");
}
```

## Next Steps

After successful integration:

1. **Test thoroughly** with the provided test cases
2. **Monitor performance** using the statistics system
3. **Implement custom features** using the UserEngine API
4. **Optimize** based on actual usage patterns
5. **Extend functionality** as needed for specific requirements

The UserEngine provides a solid foundation for the Mir200 server and can be extended to meet specific game requirements while maintaining compatibility with the original project structure.
